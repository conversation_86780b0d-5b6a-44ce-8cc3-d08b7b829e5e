# MySQL 5.6.50 兼容性说明

## 📋 版本信息
- **目标MySQL版本**：5.6.50
- **项目数据库**：cunzhe
- **字符集**：utf8mb4
- **排序规则**：utf8mb4_general_ci

## ✅ 已确认兼容的功能

### 1. 字符集支持
- ✅ **utf8mb4字符集**：MySQL 5.6.10+开始支持
- ✅ **utf8mb4_general_ci排序规则**：完全兼容
- ✅ **中文字符存储**：支持完整的Unicode字符

### 2. 数据类型
- ✅ **INT, VARCHAR, TEXT**：完全支持
- ✅ **DECIMAL(10,2)**：金额字段支持
- ✅ **DATE, TIMESTAMP**：日期时间支持
- ✅ **TINYINT(1)**：布尔值支持

### 3. 表结构特性
- ✅ **AUTO_INCREMENT**：自增主键
- ✅ **PRIMARY KEY, UNIQUE KEY**：索引支持
- ✅ **FOREIGN KEY**：外键约束
- ✅ **DEFAULT值**：默认值设置
- ✅ **COMMENT**：字段和表注释

### 4. 高级功能
- ✅ **触发器(TRIGGER)**：支持AFTER INSERT/DELETE
- ✅ **视图(VIEW)**：支持复杂查询视图
- ✅ **存储过程**：如果需要可以使用
- ✅ **事务处理**：InnoDB引擎支持

## ⚠️ 需要注意的限制

### 1. 字符集配置
```sql
-- 确保服务器配置支持utf8mb4
SHOW VARIABLES LIKE 'character_set%';
SHOW VARIABLES LIKE 'collation%';

-- 如果不支持，需要在my.cnf中配置：
[mysqld]
character-set-server=utf8mb4
collation-server=utf8mb4_general_ci

[mysql]
default-character-set=utf8mb4

[client]
default-character-set=utf8mb4
```

### 2. 时间戳默认值
```sql
-- MySQL 5.6中，只有一个TIMESTAMP字段可以有DEFAULT CURRENT_TIMESTAMP
-- 我们的设计中使用了created_at和updated_at，需要确认兼容性

-- 如果遇到问题，可以调整为：
`created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
`updated_at` timestamp DEFAULT '0000-00-00 00:00:00' ON UPDATE CURRENT_TIMESTAMP
```

### 3. 不支持的新特性
- ❌ **JSON数据类型**：MySQL 5.7+才支持，我们没有使用
- ❌ **生成列(Generated Columns)**：MySQL 5.7+，我们没有使用
- ❌ **CTE(公用表表达式)**：MySQL 8.0+，我们没有使用

## 🔧 兼容性调整

### 1. 数据库脚本调整
已在`database.sql`中进行的调整：
- 字符集从`utf8mb4_unicode_ci`改为`utf8mb4_general_ci`
- 确保所有语法与MySQL 5.6兼容
- 触发器语法验证

### 2. PHP连接配置
```php
// PDO连接示例（兼容MySQL 5.6.50）
$dsn = "mysql:host=localhost;dbname=cunzhe;charset=utf8mb4";
$options = [
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES => false,
    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_general_ci"
];
$pdo = new PDO($dsn, $username, $password, $options);
```

### 3. 小程序端配置
```javascript
// 小程序请求头设置
wx.request({
  url: 'https://cz.alidog.cn/api/v1/records',
  header: {
    'content-type': 'application/json; charset=utf-8'
  },
  // ...
})
```

## 🧪 兼容性测试清单

### 数据库测试
- [ ] 执行database.sql脚本无错误
- [ ] 插入中文数据正常显示
- [ ] 触发器正常工作
- [ ] 视图查询正常
- [ ] 外键约束生效

### 连接测试
```sql
-- 测试字符集
SELECT @@character_set_database, @@collation_database;

-- 测试中文插入
INSERT INTO users (openid, name) VALUES ('test001', '测试用户');
SELECT * FROM users WHERE name = '测试用户';

-- 测试触发器
INSERT INTO reading_records (user_id, book_name, reading_minutes, reading_date) 
VALUES (1, '测试书籍', 30, '2024-01-15');
SELECT total_books, total_minutes FROM users WHERE id = 1;
```

### PHP测试
```php
// 测试数据库连接和中文处理
try {
    $pdo = new PDO($dsn, $username, $password, $options);
    
    // 插入中文测试
    $stmt = $pdo->prepare("INSERT INTO users (openid, name) VALUES (?, ?)");
    $stmt->execute(['test002', '中文测试']);
    
    // 查询中文测试
    $stmt = $pdo->prepare("SELECT * FROM users WHERE name = ?");
    $stmt->execute(['中文测试']);
    $result = $stmt->fetch();
    
    echo "测试成功：" . $result['name'];
} catch (PDOException $e) {
    echo "错误：" . $e->getMessage();
}
```

## 🚨 常见问题解决

### 1. 字符集问题
**问题**：中文显示乱码
**解决**：
```sql
-- 检查字符集设置
SHOW CREATE DATABASE cunzhe;
SHOW CREATE TABLE users;

-- 如果字符集不对，重新设置
ALTER DATABASE cunzhe CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
ALTER TABLE users CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
```

### 2. 时间戳问题
**问题**：创建表时时间戳字段报错
**解决**：
```sql
-- 如果遇到多个TIMESTAMP字段问题，可以调整为：
`created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
`updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
```

### 3. 触发器问题
**问题**：触发器创建失败
**解决**：
```sql
-- 检查触发器语法，确保分隔符正确
DELIMITER $$
CREATE TRIGGER trigger_name
AFTER INSERT ON table_name
FOR EACH ROW
BEGIN
    -- 触发器逻辑
END$$
DELIMITER ;
```

## 📝 部署建议

### 1. 服务器配置检查
```bash
# 检查MySQL版本
mysql --version

# 检查字符集支持
mysql -e "SHOW CHARACTER SET LIKE 'utf8mb4';"

# 检查配置文件
cat /etc/mysql/my.cnf | grep character
```

### 2. 数据库初始化步骤
1. 备份现有数据（如果有）
2. 执行`database.sql`脚本
3. 运行兼容性测试
4. 插入测试数据验证
5. 测试PHP连接

### 3. 性能优化建议
```sql
-- 为MySQL 5.6优化的配置建议
[mysqld]
innodb_buffer_pool_size = 128M
innodb_log_file_size = 64M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT
query_cache_size = 32M
query_cache_type = 1
```

## ✅ 验收标准

数据库兼容性验收通过标准：
- ✅ database.sql脚本执行无错误
- ✅ 中文数据正常存储和显示
- ✅ 所有表和索引创建成功
- ✅ 触发器和视图工作正常
- ✅ PHP可以正常连接和操作数据库
- ✅ 小程序可以通过API正常访问数据

---

**注意**：MySQL 5.6.50是一个相对较老的版本，建议在条件允许的情况下升级到更新的版本以获得更好的性能和功能支持。但当前的设计完全兼容MySQL 5.6.50。
