<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>阅读存折</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 375px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(45deg, #ff9a9e 0%, #fecfef 100%);
            padding: 30px 20px;
            text-align: center;
            color: white;
        }

        .avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: #fff;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 30px;
            color: #ff9a9e;
            border: 3px solid rgba(255,255,255,0.3);
        }

        .user-name {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .user-title {
            font-size: 14px;
            opacity: 0.9;
        }

        .passbook {
            background: #f8f9ff;
            margin: 20px;
            border-radius: 15px;
            padding: 25px;
            border: 2px dashed #e0e6ff;
            position: relative;
        }

        .passbook::before {
            content: '';
            position: absolute;
            top: -10px;
            left: 20px;
            right: 20px;
            height: 20px;
            background: repeating-linear-gradient(
                90deg,
                #e0e6ff 0px,
                #e0e6ff 10px,
                transparent 10px,
                transparent 20px
            );
        }

        .balance-title {
            font-size: 16px;
            color: #666;
            margin-bottom: 10px;
            text-align: center;
        }

        .balance-amount {
            font-size: 36px;
            font-weight: bold;
            color: #4CAF50;
            text-align: center;
            margin-bottom: 20px;
        }

        .balance-unit {
            font-size: 18px;
            color: #888;
        }

        .stats-row {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
        }

        .stat-item {
            text-align: center;
            flex: 1;
        }

        .stat-number {
            font-size: 20px;
            font-weight: bold;
            color: #333;
        }

        .stat-label {
            font-size: 12px;
            color: #888;
            margin-top: 5px;
        }

        .action-buttons {
            padding: 20px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .btn {
            padding: 15px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(45deg, #2196F3, #1976D2);
            color: white;
        }

        .btn-info {
            background: linear-gradient(45deg, #FF9800, #F57C00);
            color: white;
        }

        .btn-profile {
            background: linear-gradient(45deg, #9C27B0, #7B1FA2);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .recent-records {
            margin: 20px;
            background: white;
            border-radius: 15px;
            padding: 20px;
            border: 1px solid #f0f0f0;
        }

        .records-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .record-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f5f5f5;
        }

        .record-item:last-child {
            border-bottom: none;
        }

        .record-info {
            flex: 1;
        }

        .record-book {
            font-size: 14px;
            font-weight: bold;
            color: #333;
        }

        .record-date {
            font-size: 12px;
            color: #888;
            margin-top: 2px;
        }

        .record-amount {
            font-size: 16px;
            font-weight: bold;
            color: #4CAF50;
        }

        .empty-state {
            text-align: center;
            color: #888;
            font-size: 14px;
            padding: 20px;
        }

        /* 页面切换相关样式 */
        .page {
            display: none;
        }

        .page.active {
            display: block;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }

        .form-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e6ff;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
        }

        .form-select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e6ff;
            border-radius: 8px;
            font-size: 16px;
            background: white;
        }

        .back-btn {
            background: #f5f5f5;
            color: #666;
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            margin-bottom: 20px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <!-- 主页面 -->
    <div id="homePage" class="page active">
        <div class="container">
            <div class="header">
                <div class="avatar">👧</div>
                <div class="user-name" id="userName">小明</div>
                <div class="user-title">小小阅读家</div>
            </div>

            <div class="passbook">
                <div class="balance-title">📚 阅读存折余额</div>
                <div class="balance-amount">
                    <span id="totalBalance">0</span>
                    <span class="balance-unit">元</span>
                </div>
                <div class="stats-row">
                    <div class="stat-item">
                        <div class="stat-number" id="totalBooks">0</div>
                        <div class="stat-label">本书</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="totalHours">0</div>
                        <div class="stat-label">小时</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="totalDays">0</div>
                        <div class="stat-label">天</div>
                    </div>
                </div>
            </div>

            <div class="action-buttons">
                <button class="btn btn-primary" onclick="showPage('addRecordPage')">
                    📖 添加阅读
                </button>
                <button class="btn btn-secondary" onclick="showPage('statsPage')">
                    📊 查看统计
                </button>
                <button class="btn btn-info" onclick="showPage('recordsPage')">
                    📋 阅读记录
                </button>
                <button class="btn btn-profile" onclick="showPage('profilePage')">
                    👤 个人资料
                </button>
            </div>

            <div class="recent-records">
                <div class="records-title">
                    🕒 最近阅读记录
                </div>
                <div id="recentRecordsList">
                    <div class="empty-state">
                        还没有阅读记录，快去添加第一本书吧！
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加阅读记录页面 -->
    <div id="addRecordPage" class="page">
        <div class="container">
            <div class="header">
                <button class="back-btn" onclick="showPage('homePage')">← 返回</button>
                <div class="user-name">添加阅读记录</div>
                <div class="user-title">记录你的阅读时光</div>
            </div>

            <div style="padding: 20px;">
                <form id="addRecordForm">
                    <div class="form-group">
                        <label class="form-label">📚 书籍名称</label>
                        <input type="text" class="form-input" id="bookName" placeholder="请输入书籍名称" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label">✍️ 作者</label>
                        <input type="text" class="form-input" id="bookAuthor" placeholder="请输入作者姓名">
                    </div>

                    <div class="form-group">
                        <label class="form-label">⏰ 阅读时长</label>
                        <select class="form-select" id="readingTime" required>
                            <option value="">请选择阅读时长</option>
                            <option value="15">15分钟</option>
                            <option value="30">30分钟</option>
                            <option value="45">45分钟</option>
                            <option value="60">1小时</option>
                            <option value="90">1.5小时</option>
                            <option value="120">2小时</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">📅 阅读日期</label>
                        <input type="date" class="form-input" id="readingDate" required>
                    </div>

                    <button type="submit" class="btn btn-primary" style="width: 100%; margin-top: 20px;">
                        💰 存入阅读记录
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- 个人资料页面 -->
    <div id="profilePage" class="page">
        <div class="container">
            <div class="header">
                <button class="back-btn" onclick="showPage('homePage')">← 返回</button>
                <div class="user-name">个人资料</div>
                <div class="user-title">管理你的信息</div>
            </div>

            <div style="padding: 20px;">
                <form id="profileForm">
                    <div class="form-group">
                        <label class="form-label">👤 姓名</label>
                        <input type="text" class="form-input" id="profileName" placeholder="请输入姓名">
                    </div>

                    <div class="form-group">
                        <label class="form-label">🎂 年龄</label>
                        <input type="number" class="form-input" id="profileAge" placeholder="请输入年龄" min="3" max="18">
                    </div>

                    <div class="form-group">
                        <label class="form-label">📚 最喜欢的书籍类型</label>
                        <select class="form-select" id="favoriteGenre">
                            <option value="">请选择</option>
                            <option value="童话故事">童话故事</option>
                            <option value="科普读物">科普读物</option>
                            <option value="历史故事">历史故事</option>
                            <option value="冒险小说">冒险小说</option>
                            <option value="漫画绘本">漫画绘本</option>
                            <option value="诗歌散文">诗歌散文</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">✍️ 最喜欢的作家</label>
                        <input type="text" class="form-input" id="favoriteAuthor" placeholder="请输入最喜欢的作家">
                    </div>

                    <button type="submit" class="btn btn-primary" style="width: 100%; margin-top: 20px;">
                        💾 保存资料
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- 统计页面 -->
    <div id="statsPage" class="page">
        <div class="container">
            <div class="header">
                <button class="back-btn" onclick="showPage('homePage')">← 返回</button>
                <div class="user-name">阅读统计</div>
                <div class="user-title">你的阅读成就</div>
            </div>

            <div style="padding: 20px;">
                <div class="passbook">
                    <div class="balance-title">🏆 阅读成就</div>
                    <div class="stats-row" style="margin-top: 0;">
                        <div class="stat-item">
                            <div class="stat-number" id="statsBooks">0</div>
                            <div class="stat-label">读过的书</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="statsHours">0</div>
                            <div class="stat-label">阅读时长</div>
                        </div>
                    </div>
                    <div class="stats-row">
                        <div class="stat-item">
                            <div class="stat-number" id="statsBalance">0</div>
                            <div class="stat-label">存折余额</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="statsAverage">0</div>
                            <div class="stat-label">平均每天</div>
                        </div>
                    </div>
                </div>

                <div class="recent-records">
                    <div class="records-title">🎯 阅读目标</div>
                    <div style="text-align: center; padding: 20px;">
                        <div style="font-size: 14px; color: #888; margin-bottom: 10px;">本月阅读进度</div>
                        <div style="background: #f0f0f0; height: 20px; border-radius: 10px; overflow: hidden;">
                            <div id="progressBar" style="background: linear-gradient(45deg, #4CAF50, #45a049); height: 100%; width: 0%; transition: width 0.3s ease;"></div>
                        </div>
                        <div style="font-size: 12px; color: #888; margin-top: 5px;">
                            目标：每月读10本书
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 阅读记录页面 -->
    <div id="recordsPage" class="page">
        <div class="container">
            <div class="header">
                <button class="back-btn" onclick="showPage('homePage')">← 返回</button>
                <div class="user-name">阅读记录</div>
                <div class="user-title">所有的阅读历程</div>
            </div>

            <div style="padding: 20px;">
                <div class="recent-records">
                    <div class="records-title">📚 全部记录</div>
                    <div id="allRecordsList">
                        <div class="empty-state">
                            还没有阅读记录
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 数据存储
        let userData = {
            name: '小明',
            age: 8,
            favoriteGenre: '',
            favoriteAuthor: '',
            records: []
        };

        // 页面切换
        function showPage(pageId) {
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('active');
            });
            document.getElementById(pageId).classList.add('active');

            if (pageId === 'homePage') {
                updateHomePage();
            } else if (pageId === 'statsPage') {
                updateStatsPage();
            } else if (pageId === 'recordsPage') {
                updateRecordsPage();
            } else if (pageId === 'profilePage') {
                loadProfileData();
            }
        }

        // 加载本地数据
        function loadData() {
            const saved = localStorage.getItem('readingPassbook');
            if (saved) {
                userData = JSON.parse(saved);
            }
            updateHomePage();
        }

        // 保存数据
        function saveData() {
            localStorage.setItem('readingPassbook', JSON.stringify(userData));
        }

        // 更新主页
        function updateHomePage() {
            document.getElementById('userName').textContent = userData.name;

            const totalBalance = userData.records.length;
            const totalBooks = userData.records.length;
            const totalHours = userData.records.reduce((sum, record) => sum + parseInt(record.time), 0);
            const totalDays = new Set(userData.records.map(record => record.date)).size;

            document.getElementById('totalBalance').textContent = totalBalance;
            document.getElementById('totalBooks').textContent = totalBooks;
            document.getElementById('totalHours').textContent = Math.round(totalHours / 60 * 10) / 10;
            document.getElementById('totalDays').textContent = totalDays;

            // 显示最近记录
            const recentList = document.getElementById('recentRecordsList');
            if (userData.records.length === 0) {
                recentList.innerHTML = '<div class="empty-state">还没有阅读记录，快去添加第一本书吧！</div>';
            } else {
                const recent = userData.records.slice(-3).reverse();
                recentList.innerHTML = recent.map(record => `
                    <div class="record-item">
                        <div class="record-info">
                            <div class="record-book">${record.book}</div>
                            <div class="record-date">${record.date} · ${record.time}分钟</div>
                        </div>
                        <div class="record-amount">+1元</div>
                    </div>
                `).join('');
            }
        }

        // 更新统计页面
        function updateStatsPage() {
            const totalBooks = userData.records.length;
            const totalHours = userData.records.reduce((sum, record) => sum + parseInt(record.time), 0);
            const totalBalance = userData.records.length;

            document.getElementById('statsBooks').textContent = totalBooks;
            document.getElementById('statsHours').textContent = Math.round(totalHours / 60 * 10) / 10 + 'h';
            document.getElementById('statsBalance').textContent = totalBalance + '元';

            const daysReading = new Set(userData.records.map(record => record.date)).size;
            const average = daysReading > 0 ? Math.round(totalHours / daysReading) : 0;
            document.getElementById('statsAverage').textContent = average + '分钟';

            // 更新进度条
            const progress = Math.min((totalBooks / 10) * 100, 100);
            document.getElementById('progressBar').style.width = progress + '%';
        }

        // 更新记录页面
        function updateRecordsPage() {
            const allList = document.getElementById('allRecordsList');
            if (userData.records.length === 0) {
                allList.innerHTML = '<div class="empty-state">还没有阅读记录</div>';
            } else {
                const sorted = [...userData.records].reverse();
                allList.innerHTML = sorted.map(record => `
                    <div class="record-item">
                        <div class="record-info">
                            <div class="record-book">${record.book}</div>
                            <div class="record-date">${record.date} · ${record.author || '未知作者'} · ${record.time}分钟</div>
                        </div>
                        <div class="record-amount">+1元</div>
                    </div>
                `).join('');
            }
        }

        // 加载个人资料
        function loadProfileData() {
            document.getElementById('profileName').value = userData.name || '';
            document.getElementById('profileAge').value = userData.age || '';
            document.getElementById('favoriteGenre').value = userData.favoriteGenre || '';
            document.getElementById('favoriteAuthor').value = userData.favoriteAuthor || '';
        }

        // 表单提交处理
        document.getElementById('addRecordForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const bookName = document.getElementById('bookName').value;
            const bookAuthor = document.getElementById('bookAuthor').value;
            const readingTime = document.getElementById('readingTime').value;
            const readingDate = document.getElementById('readingDate').value;

            if (!bookName || !readingTime || !readingDate) {
                alert('请填写完整信息');
                return;
            }

            userData.records.push({
                book: bookName,
                author: bookAuthor,
                time: readingTime,
                date: readingDate,
                timestamp: Date.now()
            });

            saveData();

            // 清空表单
            this.reset();

            alert('阅读记录添加成功！获得1元奖励！');
            showPage('homePage');
        });

        document.getElementById('profileForm').addEventListener('submit', function(e) {
            e.preventDefault();

            userData.name = document.getElementById('profileName').value || '小明';
            userData.age = document.getElementById('profileAge').value;
            userData.favoriteGenre = document.getElementById('favoriteGenre').value;
            userData.favoriteAuthor = document.getElementById('favoriteAuthor').value;

            saveData();
            alert('个人资料保存成功！');
            showPage('homePage');
        });

        // 设置默认日期为今天
        document.getElementById('readingDate').value = new Date().toISOString().split('T')[0];

        // 初始化
        loadData();
    </script>
</body>
</html>
