# 阅读存折小程序 - 技术设计方案

## 1. 数据库设计 (MySQL 5.6.50)

### 数据库名称：cunzhe
### 兼容版本：MySQL 5.6.50
### 字符集：utf8mb4_general_ci

### 1.1 用户表 (users)
```sql
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `openid` varchar(100) NOT NULL COMMENT '微信openid',
  `unionid` varchar(100) DEFAULT NULL COMMENT '微信unionid',
  `name` varchar(50) NOT NULL DEFAULT '小朋友' COMMENT '姓名',
  `age` int(3) DEFAULT NULL COMMENT '年龄',
  `birthday` date DEFAULT NULL COMMENT '出生日期',
  `height` decimal(5,2) DEFAULT NULL COMMENT '身高(cm)',
  `weight` decimal(5,2) DEFAULT NULL COMMENT '体重(kg)',
  `interests` text COMMENT '兴趣爱好',
  `contact` varchar(200) DEFAULT NULL COMMENT '联系方式',
  `avatar_url` varchar(500) DEFAULT NULL COMMENT '头像URL',
  `favorite_author` varchar(100) DEFAULT NULL COMMENT '最喜欢的作家',
  `favorite_genre` varchar(50) DEFAULT NULL COMMENT '最喜欢的书籍类型',
  `total_balance` decimal(10,2) DEFAULT 0.00 COMMENT '总余额',
  `total_books` int(11) DEFAULT 0 COMMENT '总书籍数',
  `total_minutes` int(11) DEFAULT 0 COMMENT '总阅读分钟数',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `openid` (`openid`),
  KEY `unionid` (`unionid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';
```

### 1.2 阅读记录表 (reading_records)
```sql
CREATE TABLE `reading_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `book_name` varchar(200) NOT NULL COMMENT '书籍名称',
  `book_author` varchar(100) DEFAULT NULL COMMENT '作者',
  `reading_date` date NOT NULL COMMENT '阅读日期',
  `reading_minutes` int(11) NOT NULL COMMENT '阅读分钟数',
  `reading_pages` int(11) DEFAULT NULL COMMENT '阅读页数',
  `amount` decimal(10,2) DEFAULT 1.00 COMMENT '存入金额',
  `notes` text COMMENT '阅读笔记',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `reading_date` (`reading_date`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='阅读记录表';
```

### 1.3 用户统计表 (user_stats)
```sql
CREATE TABLE `user_stats` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `daily_minutes` int(11) DEFAULT 0 COMMENT '当日阅读分钟数',
  `daily_books` int(11) DEFAULT 0 COMMENT '当日阅读书籍数',
  `weekly_minutes` int(11) DEFAULT 0 COMMENT '本周阅读分钟数',
  `monthly_minutes` int(11) DEFAULT 0 COMMENT '本月阅读分钟数',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_date` (`user_id`, `stat_date`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户统计表';
```

## 2. 后端API设计 (PHP RESTful)

### 2.1 基础配置
- 域名：https://cz.alidog.cn
- API前缀：/api/v1
- 认证方式：JWT Token
- 响应格式：JSON

### 2.2 通用响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": 1640995200
}
```

### 2.3 API接口列表

#### 2.3.1 用户管理
```
POST   /api/v1/auth/login          微信登录
GET    /api/v1/user/profile        获取用户资料
PUT    /api/v1/user/profile        更新用户资料
POST   /api/v1/user/upload-avatar  上传头像
POST   /api/v1/user/sync-local     同步本地数据到云端
```

#### 2.3.2 阅读记录管理
```
GET    /api/v1/records             获取阅读记录列表
POST   /api/v1/records             添加阅读记录
PUT    /api/v1/records/{id}        更新阅读记录
DELETE /api/v1/records/{id}        删除阅读记录
POST   /api/v1/records/batch       批量添加记录
```

#### 2.3.3 统计数据
```
GET    /api/v1/stats/summary       获取统计摘要
GET    /api/v1/stats/daily         获取每日统计
GET    /api/v1/stats/monthly       获取月度统计
```

## 3. 小程序前端架构

### 3.1 目录结构
```
miniprogram/
├── pages/                  # 页面
│   ├── index/             # 首页
│   ├── add-record/        # 添加记录
│   ├── records/           # 记录列表
│   ├── stats/             # 统计页面
│   └── profile/           # 个人资料
├── components/            # 组件
│   ├── record-item/       # 记录项组件
│   ├── stats-card/        # 统计卡片
│   └── passbook/          # 存折组件
├── utils/                 # 工具类
│   ├── api.js            # API封装
│   ├── storage.js        # 本地存储
│   └── auth.js           # 认证管理
├── styles/               # 样式
│   └── common.wxss       # 通用样式
└── app.js               # 应用入口
```

### 3.2 核心功能模块

#### 3.2.1 数据管理
- 本地存储：wx.setStorageSync/wx.getStorageSync
- 云端同步：API调用 + 数据合并策略
- 离线支持：本地优先，网络恢复时同步

#### 3.2.2 用户认证
- 免登录模式：仅使用本地存储
- 微信登录：wx.login + 后端验证
- 数据迁移：本地数据同步到云端

#### 3.2.3 页面功能
- 首页：存折样式 + 统计概览 + 快捷操作
- 添加记录：表单录入 + 本地存储 + 云端同步
- 记录列表：分页加载 + 搜索筛选
- 统计页面：图表展示 + 进度跟踪
- 个人资料：信息管理 + 头像上传

## 4. 技术要点

### 4.1 数据同步策略
1. 本地优先：所有操作先保存到本地
2. 后台同步：网络可用时自动同步
3. 冲突处理：以最新时间戳为准
4. 增量同步：只同步变更数据

### 4.2 性能优化
1. 分页加载：记录列表分页显示
2. 图片优化：头像压缩上传
3. 缓存策略：API响应缓存
4. 懒加载：统计图表按需加载

### 4.3 用户体验
1. 离线可用：核心功能离线支持
2. 加载状态：操作反馈提示
3. 错误处理：友好的错误提示
4. 数据备份：云端数据安全保障

## 5. API接口详细设计

### 5.1 用户认证接口

#### POST /api/v1/auth/login
**功能**：微信登录
**请求参数**：
```json
{
  "code": "微信登录code",
  "userInfo": {
    "nickName": "用户昵称",
    "avatarUrl": "头像URL"
  }
}
```
**响应数据**：
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "JWT令牌",
    "userInfo": {
      "id": 1,
      "name": "小明",
      "avatar_url": "头像URL",
      "total_balance": 10.00,
      "total_books": 10,
      "total_minutes": 600
    }
  }
}
```

### 5.2 用户资料接口

#### GET /api/v1/user/profile
**功能**：获取用户资料
**请求头**：Authorization: Bearer {token}
**响应数据**：
```json
{
  "code": 200,
  "data": {
    "id": 1,
    "name": "小明",
    "age": 8,
    "birthday": "2015-06-01",
    "height": 120.5,
    "weight": 25.0,
    "interests": "画画,唱歌",
    "contact": "妈妈:13800138000",
    "avatar_url": "头像URL",
    "favorite_author": "杨红樱",
    "favorite_genre": "童话故事"
  }
}
```

#### PUT /api/v1/user/profile
**功能**：更新用户资料
**请求参数**：
```json
{
  "name": "小明",
  "age": 8,
  "birthday": "2015-06-01",
  "height": 120.5,
  "weight": 25.0,
  "interests": "画画,唱歌",
  "contact": "妈妈:13800138000",
  "favorite_author": "杨红樱",
  "favorite_genre": "童话故事"
}
```

### 5.3 阅读记录接口

#### GET /api/v1/records
**功能**：获取阅读记录列表
**请求参数**：
- page: 页码 (默认1)
- limit: 每页数量 (默认20)
- start_date: 开始日期
- end_date: 结束日期
- keyword: 搜索关键词

**响应数据**：
```json
{
  "code": 200,
  "data": {
    "list": [
      {
        "id": 1,
        "book_name": "小王子",
        "book_author": "圣埃克苏佩里",
        "reading_date": "2024-01-15",
        "reading_minutes": 30,
        "amount": 1.00,
        "created_at": "2024-01-15 20:30:00"
      }
    ],
    "total": 50,
    "page": 1,
    "limit": 20
  }
}
```

#### POST /api/v1/records
**功能**：添加阅读记录
**请求参数**：
```json
{
  "book_name": "小王子",
  "book_author": "圣埃克苏佩里",
  "reading_date": "2024-01-15",
  "reading_minutes": 30,
  "reading_pages": 10,
  "notes": "今天读了小王子的第一章"
}
```

### 5.4 统计数据接口

#### GET /api/v1/stats/summary
**功能**：获取统计摘要
**响应数据**：
```json
{
  "code": 200,
  "data": {
    "total_balance": 25.00,
    "total_books": 25,
    "total_minutes": 750,
    "total_hours": 12.5,
    "reading_days": 15,
    "current_month": {
      "books": 8,
      "minutes": 240,
      "target_books": 10,
      "progress": 80
    },
    "recent_records": [
      {
        "book_name": "小王子",
        "reading_date": "2024-01-15",
        "reading_minutes": 30
      }
    ]
  }
}
```

## 6. 错误码定义

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |
| 1001 | 微信登录失败 |
| 1002 | 用户不存在 |
| 1003 | 记录不存在 |
| 1004 | 数据验证失败 |
