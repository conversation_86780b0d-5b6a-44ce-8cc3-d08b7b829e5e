-- 创建数据库
CREATE DATABASE IF NOT EXISTS `cunzhe` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `cunzhe`;

-- 家长用户表
CREATE TABLE `parent_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '家长用户ID',
  `openid` varchar(50) NOT NULL COMMENT '微信openid',
  `nickname` varchar(50) DEFAULT NULL COMMENT '微信昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1正常，0禁用',
  PRIMARY KEY (`id`),
  UNIQUE KEY `openid` (`openid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='家长用户表';

-- 儿童信息表
CREATE TABLE `children` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '儿童ID',
  `parent_id` int(11) NOT NULL COMMENT '关联家长ID',
  `name` varchar(50) NOT NULL COMMENT '姓名',
  `age` int(3) DEFAULT NULL COMMENT '年龄',
  `birth_date` date DEFAULT NULL COMMENT '出生日期',
  `gender` tinyint(1) DEFAULT NULL COMMENT '性别：1男，2女，0未知',
  `height` decimal(5,2) DEFAULT NULL COMMENT '身高(cm)',
  `weight` decimal(5,2) DEFAULT NULL COMMENT '体重(kg)',
  `hobbies` varchar(255) DEFAULT NULL COMMENT '兴趣爱好',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `favorite_author` varchar(100) DEFAULT NULL COMMENT '最喜欢的作家',
  `favorite_book_type` varchar(100) DEFAULT NULL COMMENT '最喜欢的书籍类型',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='儿童信息表';

-- 阅读存折表
CREATE TABLE `reading_passbooks` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '存折ID',
  `child_id` int(11) NOT NULL COMMENT '关联儿童ID',
  `cover_type` tinyint(2) DEFAULT '1' COMMENT '存折封面类型',
  `daily_target_minutes` int(5) DEFAULT '30' COMMENT '每日阅读目标(分钟)',
  `reading_promise` varchar(255) DEFAULT NULL COMMENT '阅读承诺内容',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_child_id` (`child_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='阅读存折表';

-- 阅读记录表
CREATE TABLE `reading_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `passbook_id` int(11) NOT NULL COMMENT '关联存折ID',
  `child_id` int(11) NOT NULL COMMENT '关联儿童ID',
  `book_name` varchar(100) NOT NULL COMMENT '书籍名称',
  `author` varchar(50) DEFAULT NULL COMMENT '作者',
  `reading_date` date NOT NULL COMMENT '阅读日期',
  `reading_duration` int(5) DEFAULT '0' COMMENT '阅读时长(分钟)',
  `reading_pages` int(5) DEFAULT '0' COMMENT '阅读页数',
  `deposit_amount` decimal(10,2) DEFAULT '1.00' COMMENT '存入金额，默认1元',
  `withdraw_amount` decimal(10,2) DEFAULT '0.00' COMMENT '支取金额',
  `balance` decimal(10,2) DEFAULT '0.00' COMMENT '余额',
  `operator` varchar(50) DEFAULT NULL COMMENT '操作员(家长或监护人)',
  `operator_id` int(11) DEFAULT NULL COMMENT '操作员ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_passbook_id` (`passbook_id`),
  KEY `idx_child_id` (`child_id`),
  KEY `idx_reading_date` (`reading_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='阅读记录表';

-- 阅读目标表
CREATE TABLE `reading_goals` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '目标ID',
  `child_id` int(11) NOT NULL COMMENT '关联儿童ID',
  `goal_type` tinyint(1) NOT NULL COMMENT '目标类型：1每日目标，2每周目标，3每月目标',
  `target_minutes` int(5) NOT NULL DEFAULT '0' COMMENT '目标阅读时长(分钟)',
  `target_books` int(5) NOT NULL DEFAULT '0' COMMENT '目标阅读书籍数量',
  `start_date` date NOT NULL COMMENT '开始日期',
  `end_date` date DEFAULT NULL COMMENT '结束日期',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：1进行中，2已完成，0已取消',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_child_id` (`child_id`),
  KEY `idx_date_range` (`start_date`,`end_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='阅读目标表';



-- 系统配置表
CREATE TABLE `system_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(50) NOT NULL COMMENT '配置键',
  `config_value` varchar(255) DEFAULT NULL COMMENT '配置值',
  `config_desc` varchar(255) DEFAULT NULL COMMENT '配置描述',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';
