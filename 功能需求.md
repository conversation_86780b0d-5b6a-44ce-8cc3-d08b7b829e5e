# 阅读存折小程序 - MVP功能需求

## 项目概述
阅读存折是一款鼓励儿童培养阅读习惯的小程序，通过模拟银行存折的方式记录孩子的阅读活动，让阅读变得更有趣味性和持续性。

## 技术架构
- 前端：微信原生小程序
- 后端：PHP
- 域名：https://cz.alidog.cn

## 核心功能需求

### 1. 用户管理
- 免登入录入数据保存到用户本地，点保存到云端的时候需要用户点击确认登入获取用户微信id，登入后保存到用户云端
- 儿童个人资料管理
  - 基本信息：姓名、年龄、出生日期
  - 个性化信息：兴趣爱好、身高体重
  - 联系方式
  - 头像上传
  - 阅读偏好（最喜欢的作家、书籍类型）

### 2. 阅读记录管理
- 添加阅读记录
  - 日期记录
  - 书籍名称及作者
  - 阅读时长或页数
- "存入"功能（每次阅读计为1元存入）
- "支取"功能（累计金额可查看）
- 余额显示

### 3. 统计与激励
- 阅读数据统计（总阅读时间、书籍数量等）

### 4. 其他需求
- 界面设计简洁、色彩丰富、符合儿童审美
- 操作流程简单直观
- 页面加载速度快

