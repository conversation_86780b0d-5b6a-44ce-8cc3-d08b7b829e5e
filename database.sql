-- 阅读存折小程序数据库设计
-- 数据库名称：cunzhe
-- 创建时间：2024-01-15
-- 版本：v1.0

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `cunzhe` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `cunzhe`;

-- 1. 用户表
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `openid` varchar(100) NOT NULL COMMENT '微信openid',
  `unionid` varchar(100) DEFAULT NULL COMMENT '微信unionid',
  `name` varchar(50) NOT NULL DEFAULT '小朋友' COMMENT '姓名',
  `age` int(3) DEFAULT NULL COMMENT '年龄',
  `birthday` date DEFAULT NULL COMMENT '出生日期',
  `height` decimal(5,2) DEFAULT NULL COMMENT '身高(cm)',
  `weight` decimal(5,2) DEFAULT NULL COMMENT '体重(kg)',
  `interests` text COMMENT '兴趣爱好',
  `contact` varchar(200) DEFAULT NULL COMMENT '联系方式',
  `avatar_url` varchar(500) DEFAULT NULL COMMENT '头像URL',
  `favorite_author` varchar(100) DEFAULT NULL COMMENT '最喜欢的作家',
  `favorite_genre` varchar(50) DEFAULT NULL COMMENT '最喜欢的书籍类型',
  `total_balance` decimal(10,2) DEFAULT 0.00 COMMENT '总余额',
  `total_books` int(11) DEFAULT 0 COMMENT '总书籍数',
  `total_minutes` int(11) DEFAULT 0 COMMENT '总阅读分钟数',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `openid` (`openid`),
  KEY `unionid` (`unionid`),
  KEY `status` (`status`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 2. 阅读记录表
DROP TABLE IF EXISTS `reading_records`;
CREATE TABLE `reading_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `book_name` varchar(200) NOT NULL COMMENT '书籍名称',
  `book_author` varchar(100) DEFAULT NULL COMMENT '作者',
  `reading_date` date NOT NULL COMMENT '阅读日期',
  `reading_minutes` int(11) NOT NULL COMMENT '阅读分钟数',
  `reading_pages` int(11) DEFAULT NULL COMMENT '阅读页数',
  `amount` decimal(10,2) DEFAULT 1.00 COMMENT '存入金额',
  `notes` text COMMENT '阅读笔记',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-正常，0-删除',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `reading_date` (`reading_date`),
  KEY `status` (`status`),
  KEY `created_at` (`created_at`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='阅读记录表';

-- 3. 用户统计表
DROP TABLE IF EXISTS `user_stats`;
CREATE TABLE `user_stats` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `daily_minutes` int(11) DEFAULT 0 COMMENT '当日阅读分钟数',
  `daily_books` int(11) DEFAULT 0 COMMENT '当日阅读书籍数',
  `weekly_minutes` int(11) DEFAULT 0 COMMENT '本周阅读分钟数',
  `weekly_books` int(11) DEFAULT 0 COMMENT '本周阅读书籍数',
  `monthly_minutes` int(11) DEFAULT 0 COMMENT '本月阅读分钟数',
  `monthly_books` int(11) DEFAULT 0 COMMENT '本月阅读书籍数',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_date` (`user_id`, `stat_date`),
  KEY `stat_date` (`stat_date`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户统计表';

-- 4. 系统配置表
DROP TABLE IF EXISTS `system_config`;
CREATE TABLE `system_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` text COMMENT '配置值',
  `config_desc` varchar(200) DEFAULT NULL COMMENT '配置描述',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 插入默认配置
INSERT INTO `system_config` (`config_key`, `config_value`, `config_desc`) VALUES
('reading_amount_per_record', '1.00', '每次阅读记录的存入金额'),
('monthly_reading_target', '10', '月度阅读目标书籍数'),
('app_version', '1.0.0', '应用版本号'),
('maintenance_mode', '0', '维护模式：1-开启，0-关闭');

-- 创建触发器：更新用户统计数据
DELIMITER $$

-- 添加阅读记录时更新用户统计
CREATE TRIGGER `update_user_stats_after_insert` 
AFTER INSERT ON `reading_records` 
FOR EACH ROW 
BEGIN
    -- 更新用户总统计
    UPDATE `users` SET 
        `total_books` = `total_books` + 1,
        `total_minutes` = `total_minutes` + NEW.reading_minutes,
        `total_balance` = `total_balance` + NEW.amount,
        `updated_at` = CURRENT_TIMESTAMP
    WHERE `id` = NEW.user_id;
    
    -- 更新或插入日统计
    INSERT INTO `user_stats` (
        `user_id`, `stat_date`, `daily_minutes`, `daily_books`
    ) VALUES (
        NEW.user_id, NEW.reading_date, NEW.reading_minutes, 1
    ) ON DUPLICATE KEY UPDATE
        `daily_minutes` = `daily_minutes` + NEW.reading_minutes,
        `daily_books` = `daily_books` + 1,
        `updated_at` = CURRENT_TIMESTAMP;
END$$

-- 删除阅读记录时更新用户统计
CREATE TRIGGER `update_user_stats_after_delete` 
AFTER DELETE ON `reading_records` 
FOR EACH ROW 
BEGIN
    -- 更新用户总统计
    UPDATE `users` SET 
        `total_books` = GREATEST(`total_books` - 1, 0),
        `total_minutes` = GREATEST(`total_minutes` - OLD.reading_minutes, 0),
        `total_balance` = GREATEST(`total_balance` - OLD.amount, 0),
        `updated_at` = CURRENT_TIMESTAMP
    WHERE `id` = OLD.user_id;
    
    -- 更新日统计
    UPDATE `user_stats` SET
        `daily_minutes` = GREATEST(`daily_minutes` - OLD.reading_minutes, 0),
        `daily_books` = GREATEST(`daily_books` - 1, 0),
        `updated_at` = CURRENT_TIMESTAMP
    WHERE `user_id` = OLD.user_id AND `stat_date` = OLD.reading_date;
END$$

DELIMITER ;

-- 创建视图：用户阅读统计视图
CREATE VIEW `v_user_reading_stats` AS
SELECT 
    u.id,
    u.name,
    u.total_books,
    u.total_minutes,
    u.total_balance,
    ROUND(u.total_minutes / 60, 1) AS total_hours,
    COUNT(DISTINCT r.reading_date) AS reading_days,
    MAX(r.reading_date) AS last_reading_date,
    -- 本月统计
    COUNT(CASE WHEN DATE_FORMAT(r.reading_date, '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m') THEN 1 END) AS current_month_books,
    SUM(CASE WHEN DATE_FORMAT(r.reading_date, '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m') THEN r.reading_minutes ELSE 0 END) AS current_month_minutes,
    -- 本周统计
    COUNT(CASE WHEN YEARWEEK(r.reading_date, 1) = YEARWEEK(CURDATE(), 1) THEN 1 END) AS current_week_books,
    SUM(CASE WHEN YEARWEEK(r.reading_date, 1) = YEARWEEK(CURDATE(), 1) THEN r.reading_minutes ELSE 0 END) AS current_week_minutes
FROM `users` u
LEFT JOIN `reading_records` r ON u.id = r.user_id AND r.status = 1
WHERE u.status = 1
GROUP BY u.id;

-- 创建索引优化查询性能
CREATE INDEX `idx_reading_records_user_date` ON `reading_records` (`user_id`, `reading_date`);
CREATE INDEX `idx_reading_records_date_status` ON `reading_records` (`reading_date`, `status`);
CREATE INDEX `idx_user_stats_user_date` ON `user_stats` (`user_id`, `stat_date`);

-- 插入测试数据（可选）
-- INSERT INTO `users` (`openid`, `name`, `age`, `favorite_genre`) VALUES
-- ('test_openid_001', '小明', 8, '童话故事'),
-- ('test_openid_002', '小红', 7, '科普读物');

-- INSERT INTO `reading_records` (`user_id`, `book_name`, `book_author`, `reading_date`, `reading_minutes`) VALUES
-- (1, '小王子', '圣埃克苏佩里', '2024-01-15', 30),
-- (1, '哈利波特', 'J.K.罗琳', '2024-01-16', 45),
-- (2, '十万个为什么', '叶永烈', '2024-01-15', 25);

-- 数据库初始化完成
SELECT 'Database initialization completed successfully!' AS message;
